const aiService = require('../utils/aiService');
const Conversation = require('../models/Conversation');
const LearningRecord = require('../models/LearningRecord');

class AIController {
  // AI对话接口（匹配前端需求）
  async chat(req, res) {
    try {
      const { message, conversationId, settings = {} } = req.body;
      const user = req.user;

      if (!message || !message.trim()) {
        return res.status(400).json({
          error: '参数错误',
          message: '消息内容不能为空'
        });
      }

      // 检查AI服务是否可用
      if (!aiService.isAvailable()) {
        return res.status(503).json({
          error: 'AI服务不可用',
          message: '请稍后重试或联系管理员'
        });
      }

      let conversation;
      
      // 获取或创建对话
      if (conversationId) {
        try {
          // 验证conversationId是否为有效的ObjectId格式
          if (!conversationId.match(/^[0-9a-fA-F]{24}$/)) {
            console.log('无效的conversationId格式，创建新对话:', conversationId);
            conversation = null;
          } else {
            conversation = await Conversation.findOne({
              _id: conversationId,
              userId: user._id
            });
          }
        } catch (error) {
          console.log('查找对话时出错，创建新对话:', error.message);
          conversation = null;
        }

        if (!conversation) {
          console.log('对话不存在或无效，创建新对话');
          // 不返回错误，而是创建新对话
        }
      }

      if (!conversation) {
        // 创建新对话
        conversation = new Conversation({
          userId: user._id,
          title: `对话 ${new Date().toLocaleString()}`,
          settings: {
            language: settings.language || user.settings.speechRecognition.language,
            difficulty: user.learningStats.level,
            systemPrompt: settings.systemPrompt || user.settings.aiSettings.systemPrompt
          }
        });
      }

      // 添加用户消息
      const userMessage = {
        content: message.trim(),
        type: 'user',
        timestamp: new Date(),
        userMetadata: {
          inputMethod: settings.inputMethod || 'text'
        }
      };

      conversation.messages.push(userMessage);

      // 准备AI对话选项
      const aiOptions = {
        systemPrompt: conversation.settings.systemPrompt || user.settings.aiSettings.systemPrompt,
        conversationHistory: conversation.messages.slice(-10), // 最近10条消息
        userLevel: user.learningStats.level,
        maxTokens: user.settings.aiSettings.maxTokens
      };

      // 生成AI回复
      const aiResponse = await aiService.generateChatResponse(message, aiOptions);

      // 添加AI消息
      const aiMessage = {
        content: aiResponse.content,
        type: 'ai',
        timestamp: new Date(),
        aiMetadata: aiResponse.metadata
      };

      conversation.messages.push(aiMessage);

      // 保存对话
      await conversation.save();

      // 更新用户学习统计
      user.learningStats.totalMessages += 2; // 用户消息 + AI消息
      if (!conversationId) {
        user.learningStats.totalConversations += 1;
      }
      user.learningStats.lastActiveDate = new Date();
      await user.save();

      // 记录学习记录
      const learningRecord = new LearningRecord({
        userId: user._id,
        type: 'conversation',
        contentId: conversation._id,
        contentType: 'Conversation',
        sessionId: `chat_${Date.now()}`,
        result: {
          status: 'completed',
          timeSpent: aiResponse.metadata.responseTime / 1000,
          details: {
            totalQuestions: 1,
            correctAnswers: 1 // 对话没有对错，默认为正确
          }
        },
        startTime: userMessage.timestamp,
        endTime: aiMessage.timestamp,
        studyDate: new Date()
      });

      await learningRecord.save();

      // 返回响应（匹配前端期望的格式）
      res.json({
        success: true,
        conversation: {
          id: conversation._id,
          title: conversation.title,
          messages: conversation.messages.slice(-2), // 返回最新的用户消息和AI回复
          updatedAt: conversation.updatedAt
        },
        aiResponse: {
          content: aiResponse.content,
          metadata: aiResponse.metadata
        }
      });

    } catch (error) {
      console.error('AI对话错误:', error);
      res.status(500).json({
        error: 'AI对话失败',
        message: error.message || '服务器内部错误'
      });
    }
  }

  // 获取对话历史（匹配前端需求）
  async getConversations(req, res) {
    try {
      const user = req.user;
      const { page = 1, limit = 20, status } = req.query;

      const options = {
        status,
        limit: parseInt(limit),
        skip: (parseInt(page) - 1) * parseInt(limit)
      };

      const conversations = await Conversation.getUserConversations(user._id, options);

      // 获取总数
      const total = await Conversation.countDocuments({
        userId: user._id,
        ...(status && { status })
      });

      res.json({
        conversations,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      });

    } catch (error) {
      console.error('获取对话历史错误:', error);
      res.status(500).json({
        error: '获取对话历史失败',
        message: '服务器内部错误'
      });
    }
  }

  // 获取单个对话详情
  async getConversation(req, res) {
    try {
      const { conversationId } = req.params;
      const user = req.user;

      const conversation = await Conversation.findOne({
        _id: conversationId,
        userId: user._id
      });

      if (!conversation) {
        return res.status(404).json({
          error: '对话不存在',
          message: '指定的对话不存在或您没有权限访问'
        });
      }

      res.json({
        conversation
      });

    } catch (error) {
      console.error('获取对话详情错误:', error);
      res.status(500).json({
        error: '获取对话详情失败',
        message: '服务器内部错误'
      });
    }
  }

  // 删除对话
  async deleteConversation(req, res) {
    try {
      const { conversationId } = req.params;
      const user = req.user;

      const conversation = await Conversation.findOneAndDelete({
        _id: conversationId,
        userId: user._id
      });

      if (!conversation) {
        return res.status(404).json({
          error: '对话不存在',
          message: '指定的对话不存在或您没有权限删除'
        });
      }

      res.json({
        message: '对话删除成功'
      });

    } catch (error) {
      console.error('删除对话错误:', error);
      res.status(500).json({
        error: '删除对话失败',
        message: '服务器内部错误'
      });
    }
  }

  // 语法检查
  async checkGrammar(req, res) {
    try {
      const { text, language = 'english' } = req.body;

      if (!text || !text.trim()) {
        return res.status(400).json({
          error: '参数错误',
          message: '文本内容不能为空'
        });
      }

      if (!aiService.isAvailable()) {
        return res.status(503).json({
          error: 'AI服务不可用',
          message: '请稍后重试或联系管理员'
        });
      }

      const result = await aiService.checkGrammar(text, { language });

      res.json({
        success: true,
        result
      });

    } catch (error) {
      console.error('语法检查错误:', error);
      res.status(500).json({
        error: '语法检查失败',
        message: error.message || '服务器内部错误'
      });
    }
  }

  // 作文批改
  async reviewEssay(req, res) {
    try {
      const { essay, topic, level } = req.body;
      const user = req.user;

      if (!essay || !essay.trim()) {
        return res.status(400).json({
          error: '参数错误',
          message: '作文内容不能为空'
        });
      }

      if (!aiService.isAvailable()) {
        return res.status(503).json({
          error: 'AI服务不可用',
          message: '请稍后重试或联系管理员'
        });
      }

      const options = {
        topic: topic || '',
        level: level || user.learningStats.level
      };

      const result = await aiService.reviewEssay(essay, options);

      res.json({
        success: true,
        result
      });

    } catch (error) {
      console.error('作文批改错误:', error);
      res.status(500).json({
        error: '作文批改失败',
        message: error.message || '服务器内部错误'
      });
    }
  }

  // 获取AI服务状态
  async getStatus(req, res) {
    try {
      const status = aiService.getStatus();
      res.json(status);
    } catch (error) {
      console.error('获取AI状态错误:', error);
      res.status(500).json({
        error: '获取AI状态失败',
        message: '服务器内部错误'
      });
    }
  }
}

module.exports = new AIController();
