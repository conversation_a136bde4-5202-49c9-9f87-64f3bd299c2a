const express = require('express');
const cors = require('cors');
const config = require('./config');

// 创建 Express 应用
const app = express();

// 中间件配置
app.use(cors(config.cors));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 基本路由
app.get('/', (req, res) => {
  res.json({
    message: '欢迎使用英语学习后端API',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString()
  });
});

// 健康检查路由
app.get('/health', async (req, res) => {
  try {
    const dbHealth = await require('./config/database').healthCheck();

    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      database: dbHealth,
      version: '1.0.0'
    });
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

// API 路由 (稍后添加)
// app.use('/api/auth', require('./routes/auth'));
// app.use('/api/users', require('./routes/users'));
// app.use('/api/words', require('./routes/words'));
// app.use('/api/grammar', require('./routes/grammar'));
// app.use('/api/reading', require('./routes/reading'));
// app.use('/api/ai', require('./routes/ai'));

// 404 处理
app.use('*', (req, res) => {
  res.status(404).json({
    error: '路由未找到',
    message: `无法找到 ${req.method} ${req.originalUrl} 路由`
  });
});

// 全局错误处理中间件
app.use((err, req, res, next) => {
  console.error('错误详情:', err);
  
  // 默认错误响应
  const errorResponse = {
    error: '服务器内部错误',
    message: config.nodeEnv === 'development' ? err.message : '请稍后重试'
  };
  
  // 根据错误类型设置状态码
  let statusCode = 500;
  
  if (err.name === 'ValidationError') {
    statusCode = 400;
    errorResponse.error = '数据验证失败';
    errorResponse.details = err.errors;
  } else if (err.name === 'CastError') {
    statusCode = 400;
    errorResponse.error = '无效的数据格式';
  } else if (err.code === 11000) {
    statusCode = 409;
    errorResponse.error = '数据已存在';
  } else if (err.name === 'JsonWebTokenError') {
    statusCode = 401;
    errorResponse.error = '无效的认证令牌';
  } else if (err.name === 'TokenExpiredError') {
    statusCode = 401;
    errorResponse.error = '认证令牌已过期';
  }
  
  res.status(statusCode).json(errorResponse);
});

// 启动服务器
const startServer = async () => {
  try {
    // 连接数据库
    await require('./config/database').connect();
    
    const server = app.listen(config.port, () => {
      console.log(`🚀 服务器运行在端口 ${config.port}`);
      console.log(`📝 环境: ${config.nodeEnv}`);
      console.log(`🌐 访问地址: http://localhost:${config.port}`);
      console.log(`📊 健康检查: http://localhost:${config.port}/health`);
    });
    
    // 优雅关闭处理
    const gracefulShutdown = (signal) => {
      console.log(`\n收到 ${signal} 信号，正在优雅关闭服务器...`);
      server.close(() => {
        console.log('服务器已关闭');
        process.exit(0);
      });
    };
    
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    
  } catch (error) {
    console.error('启动服务器失败:', error);
    process.exit(1);
  }
};

// 启动应用
if (require.main === module) {
  startServer();
}

module.exports = app;
