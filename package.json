{"name": "englishbackend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1", "test:watch": "nodemon --exec \"npm test\"", "lint": "echo \"<PERSON><PERSON> not configured yet\"", "build": "echo \"Build process not configured yet\""}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@google/generative-ai": "^0.24.1", "cors": "^2.8.5", "dotenv": "^17.0.0", "express": "^4.19.2", "mongoose": "^8.16.1"}, "devDependencies": {"nodemon": "^3.1.10"}}